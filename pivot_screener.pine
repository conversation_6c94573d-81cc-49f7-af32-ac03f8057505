//@version=6
indicator("Pivot Points Screener", "Pivot Screener", overlay=false, max_lines_count=500, max_labels_count=500)

// Input settings
showWeekly = input.bool(true, "Show Weekly Pivots", group="Timeframes")
showMonthly = input.bool(true, "Show Monthly Pivots", group="Timeframes")
showYearly = input.bool(true, "Show Yearly Pivots", group="Timeframes")
tablePosition = input.string("top_right", "Table Position", options=["top_left", "top_center", "top_right", "middle_left", "middle_center", "middle_right", "bottom_left", "bottom_center", "bottom_right"], group="Display")
tableSize = input.string("normal", "Table Size", options=["tiny", "small", "normal", "large"], group="Display")

// Colors
pivotColor = input.color(color.yellow, "Pivot Color", group="Colors")
supportColor = input.color(color.green, "Support Color", group="Colors")
resistanceColor = input.color(color.red, "Resistance Color", group="Colors")
textColor = input.color(color.white, "Text Color", group="Colors")
bgColor = input.color(color.new(color.black, 80), "Background Color", group="Colors")

// Function to calculate traditional pivot points
calcPivots(h, l, c) =>
    pivot = (h + l + c) / 3
    r1 = 2 * pivot - l
    s1 = 2 * pivot - h
    r2 = pivot + (h - l)
    s2 = pivot - (h - l)
    r3 = h + 2 * (pivot - l)
    s3 = l - 2 * (h - pivot)
    r4 = pivot + 3 * (h - l)
    s4 = pivot - 3 * (h - l)
    r5 = h + 4 * (pivot - l)
    s5 = l - 4 * (h - pivot)
    [s5, s4, s3, s2, s1, pivot, r1, r2, r3, r4, r5]

// Function to find closest pivot level
findClosestPivot(currentPrice, pivotLevels) =>
    var string closestLevel = ""
    var float closestDistance = 999999.0
    var color levelColor = color.white
    
    levelNames = array.from("S5", "S4", "S3", "S2", "S1", "P", "R1", "R2", "R3", "R4", "R5")
    
    for i = 0 to array.size(pivotLevels) - 1
        level = array.get(pivotLevels, i)
        distance = math.abs(currentPrice - level)
        if distance < closestDistance
            closestDistance := distance
            closestLevel := array.get(levelNames, i)
            levelColor := i < 5 ? supportColor : (i == 5 ? pivotColor : resistanceColor)
    
    [closestLevel, closestDistance, levelColor]

// Get pivot data for different timeframes
weeklyData = request.security(syminfo.tickerid, "1W", [high, low, close], lookahead=barmerge.lookahead_on)
monthlyData = request.security(syminfo.tickerid, "1M", [high, low, close], lookahead=barmerge.lookahead_on)
yearlyData = request.security(syminfo.tickerid, "12M", [high, low, close], lookahead=barmerge.lookahead_on)

// Calculate pivot levels
weeklyPivots = calcPivots(weeklyData[0], weeklyData[1], weeklyData[2])
monthlyPivots = calcPivots(monthlyData[0], monthlyData[1], monthlyData[2])
yearlyPivots = calcPivots(yearlyData[0], yearlyData[1], yearlyData[2])

// Current price
currentPrice = close

// Find closest pivots
[weeklyClosest, weeklyDistance, weeklyColor] = findClosestPivot(currentPrice, weeklyPivots)
[monthlyClosest, monthlyDistance, monthlyColor] = findClosestPivot(currentPrice, monthlyPivots)
[yearlyClosest, yearlyDistance, yearlyColor] = findClosestPivot(currentPrice, yearlyPivots)

// Create table
var table pivotTable = na
if barstate.islast
    // Delete previous table
    if not na(pivotTable)
        table.delete(pivotTable)
    
    // Calculate number of rows needed
    numRows = 1 + (showWeekly ? 1 : 0) + (showMonthly ? 1 : 0) + (showYearly ? 1 : 0)
    
    // Create new table
    pivotTable := table.new(
         position = tablePosition == "top_left" ? position.top_left :
                   tablePosition == "top_center" ? position.top_center :
                   tablePosition == "top_right" ? position.top_right :
                   tablePosition == "middle_left" ? position.middle_left :
                   tablePosition == "middle_center" ? position.middle_center :
                   tablePosition == "middle_right" ? position.middle_right :
                   tablePosition == "bottom_left" ? position.bottom_left :
                   tablePosition == "bottom_center" ? position.bottom_center :
                   position.bottom_right,
         columns = 4,
         rows = numRows,
         bgcolor = bgColor,
         border_width = 1,
         border_color = color.gray)
    
    // Header row
    table.cell(pivotTable, 0, 0, "Timeframe", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large, bgcolor=color.new(color.gray, 70))
    table.cell(pivotTable, 1, 0, "Closest Level", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large, bgcolor=color.new(color.gray, 70))
    table.cell(pivotTable, 2, 0, "Distance", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large, bgcolor=color.new(color.gray, 70))
    table.cell(pivotTable, 3, 0, "Price", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large, bgcolor=color.new(color.gray, 70))
    
    rowIndex = 1
    
    // Weekly row
    if showWeekly
        weeklyPrice = currentPrice + (weeklyClosest == "P" ? 0 : 
                     weeklyClosest == "S1" ? weeklyPivots[4] - currentPrice :
                     weeklyClosest == "S2" ? weeklyPivots[3] - currentPrice :
                     weeklyClosest == "S3" ? weeklyPivots[2] - currentPrice :
                     weeklyClosest == "S4" ? weeklyPivots[1] - currentPrice :
                     weeklyClosest == "S5" ? weeklyPivots[0] - currentPrice :
                     weeklyClosest == "R1" ? weeklyPivots[6] - currentPrice :
                     weeklyClosest == "R2" ? weeklyPivots[7] - currentPrice :
                     weeklyClosest == "R3" ? weeklyPivots[8] - currentPrice :
                     weeklyClosest == "R4" ? weeklyPivots[9] - currentPrice :
                     weeklyPivots[10] - currentPrice)
        
        table.cell(pivotTable, 0, rowIndex, "Weekly", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 1, rowIndex, weeklyClosest, text_color=weeklyColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 2, rowIndex, str.tostring(weeklyDistance, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        actualWeeklyPrice = weeklyClosest == "P" ? weeklyPivots[5] :
                           weeklyClosest == "S1" ? weeklyPivots[4] :
                           weeklyClosest == "S2" ? weeklyPivots[3] :
                           weeklyClosest == "S3" ? weeklyPivots[2] :
                           weeklyClosest == "S4" ? weeklyPivots[1] :
                           weeklyClosest == "S5" ? weeklyPivots[0] :
                           weeklyClosest == "R1" ? weeklyPivots[6] :
                           weeklyClosest == "R2" ? weeklyPivots[7] :
                           weeklyClosest == "R3" ? weeklyPivots[8] :
                           weeklyClosest == "R4" ? weeklyPivots[9] :
                           weeklyPivots[10]

        table.cell(pivotTable, 3, rowIndex, str.tostring(actualWeeklyPrice, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        rowIndex += 1

    // Monthly row
    if showMonthly
        actualMonthlyPrice = monthlyClosest == "P" ? monthlyPivots[5] :
                            monthlyClosest == "S1" ? monthlyPivots[4] :
                            monthlyClosest == "S2" ? monthlyPivots[3] :
                            monthlyClosest == "S3" ? monthlyPivots[2] :
                            monthlyClosest == "S4" ? monthlyPivots[1] :
                            monthlyClosest == "S5" ? monthlyPivots[0] :
                            monthlyClosest == "R1" ? monthlyPivots[6] :
                            monthlyClosest == "R2" ? monthlyPivots[7] :
                            monthlyClosest == "R3" ? monthlyPivots[8] :
                            monthlyClosest == "R4" ? monthlyPivots[9] :
                            monthlyPivots[10]

        table.cell(pivotTable, 0, rowIndex, "Monthly", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 1, rowIndex, monthlyClosest, text_color=monthlyColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 2, rowIndex, str.tostring(monthlyDistance, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 3, rowIndex, str.tostring(actualMonthlyPrice, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        rowIndex += 1

    // Yearly row
    if showYearly
        actualYearlyPrice = yearlyClosest == "P" ? yearlyPivots[5] :
                           yearlyClosest == "S1" ? yearlyPivots[4] :
                           yearlyClosest == "S2" ? yearlyPivots[3] :
                           yearlyClosest == "S3" ? yearlyPivots[2] :
                           yearlyClosest == "S4" ? yearlyPivots[1] :
                           yearlyClosest == "S5" ? yearlyPivots[0] :
                           yearlyClosest == "R1" ? yearlyPivots[6] :
                           yearlyClosest == "R2" ? yearlyPivots[7] :
                           yearlyClosest == "R3" ? yearlyPivots[8] :
                           yearlyClosest == "R4" ? yearlyPivots[9] :
                           yearlyPivots[10]

        table.cell(pivotTable, 0, rowIndex, "Yearly", text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 1, rowIndex, yearlyClosest, text_color=yearlyColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 2, rowIndex, str.tostring(yearlyDistance, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)
        table.cell(pivotTable, 3, rowIndex, str.tostring(actualYearlyPrice, "#.##"), text_color=textColor, text_size=tableSize == "tiny" ? size.tiny : tableSize == "small" ? size.small : tableSize == "normal" ? size.normal : size.large)

// Plot current price for reference
plot(currentPrice, "Current Price", color=color.blue, linewidth=2)

// Optional: Plot the closest pivot levels as horizontal lines
weeklyLevel = weeklyClosest == "P" ? weeklyPivots[5] :
             weeklyClosest == "S1" ? weeklyPivots[4] :
             weeklyClosest == "S2" ? weeklyPivots[3] :
             weeklyClosest == "S3" ? weeklyPivots[2] :
             weeklyClosest == "S4" ? weeklyPivots[1] :
             weeklyClosest == "S5" ? weeklyPivots[0] :
             weeklyClosest == "R1" ? weeklyPivots[6] :
             weeklyClosest == "R2" ? weeklyPivots[7] :
             weeklyClosest == "R3" ? weeklyPivots[8] :
             weeklyClosest == "R4" ? weeklyPivots[9] :
             weeklyPivots[10]

monthlyLevel = monthlyClosest == "P" ? monthlyPivots[5] :
              monthlyClosest == "S1" ? monthlyPivots[4] :
              monthlyClosest == "S2" ? monthlyPivots[3] :
              monthlyClosest == "S3" ? monthlyPivots[2] :
              monthlyClosest == "S4" ? monthlyPivots[1] :
              monthlyClosest == "S5" ? monthlyPivots[0] :
              monthlyClosest == "R1" ? monthlyPivots[6] :
              monthlyClosest == "R2" ? monthlyPivots[7] :
              monthlyClosest == "R3" ? monthlyPivots[8] :
              monthlyClosest == "R4" ? monthlyPivots[9] :
              monthlyPivots[10]

yearlyLevel = yearlyClosest == "P" ? yearlyPivots[5] :
             yearlyClosest == "S1" ? yearlyPivots[4] :
             yearlyClosest == "S2" ? yearlyPivots[3] :
             yearlyClosest == "S3" ? yearlyPivots[2] :
             yearlyClosest == "S4" ? yearlyPivots[1] :
             yearlyClosest == "S5" ? yearlyPivots[0] :
             yearlyClosest == "R1" ? yearlyPivots[6] :
             yearlyClosest == "R2" ? yearlyPivots[7] :
             yearlyClosest == "R3" ? yearlyPivots[8] :
             yearlyClosest == "R4" ? yearlyPivots[9] :
             yearlyPivots[10]

// Plot the closest levels as reference lines (optional)
hline(weeklyLevel, "Weekly " + weeklyClosest, color=color.new(weeklyColor, 70), linestyle=hline.style_dashed)
hline(monthlyLevel, "Monthly " + monthlyClosest, color=color.new(monthlyColor, 70), linestyle=hline.style_dotted)
hline(yearlyLevel, "Yearly " + yearlyClosest, color=color.new(yearlyColor, 70), linestyle=hline.style_solid)
